# 28_gang - Gang Territory System s Reputačním Systémem

Kompletní gang territory systém pro FiveM s QBox frameworkem, OX inventářem a pokročilým reputačním systémem.

## 🎯 Hlavní Funkce

- **Freecam Editor**: Vytváření a úprava gang teritorií pomocí freecam módu
- **Spray Systém**: Sprejování pro převzetí nepřátelských teritorií
- **Reputační Systém**: Získávání reputace za aktivity a převzetí teritorií
- **Gang Management**: Kompletní správa gangů, členů a hodností
- **Drug Sales Integrace**: Integrace s Lation Drug Sales pro reputaci
- **Gang Territories**: Vizuální zobrazení teritorií s blipy a markery
- **Databázová integrace**: Perzistentní ukládání teritorií, s<PERSON><PERSON><PERSON><PERSON>, reputace a gangů
- **Konfigurovatelný**: Rozsáhlé možnosti nastavení v config.lua včetně reputačních požadavků
- **UI Interface**: Moderní webové rozhraní pro správu gangu, reputace a členů

## 📦 Instalace

### Krok 1: Základní instalace
1. **Zkopírujte script** do složky `resources/[scripts]/[DVRP]/28_gang`

2. **Přidejte do server.cfg**:
   ```
   ensure 28_gang
   ```

3. **Závislosti**:
   - qbx_core (povinné)
   - ox_inventory (povinné)
   - oxmysql (povinné)
   - lation_drugsales (volitelné - pro drug sales integraci)

### Krok 2: Databáze
- **Databázové tabulky se vytvoří automaticky** při prvním spuštění
- Vytvoří se 5 tabulek: `gang_territories`, `gang_sprays`, `gang_reputation`, `gang_members`, `gang_info`

### Krok 3: Přidání spray_can itemu
Přidejte do vašeho OX inventory items.lua:
```lua
['spray_can'] = {
    label = 'Spray Can',
    weight = 500,
    stack = true,
    close = true,
    description = 'Sprej pro označování teritorií'
}
```

### Krok 4: Lation Drug Sales integrace (volitelné)
Pokud používáte Lation Drug Sales, script automaticky detekuje prodeje drog a přidává reputaci.

## Konfigurace

Upravte `config.lua` podle vašich potřeb:

### Základní nastavení
```lua
Config.Debug = true -- Zapnout debug režim
Config.Locale = 'cs' -- Jazyk
```

### Gang nastavení
```lua
Config.Gangs = {
    ['ballas'] = {
        name = 'Ballas',
        color = {r = 128, g = 0, b = 128, a = 100},
        sprayColor = {r = 128, g = 0, b = 128},
        blip = { sprite = 84, color = 27 }
    }
}
```

### Spray systém
```lua
Config.Spray = {
    sprayItem = 'spray_can',     -- Item potřebný pro sprejování
    sprayTime = 10000,           -- Čas sprejování (ms)
    cooldownTime = 300000,       -- Cooldown mezi spreji (ms)
    requiredSprays = 3,          -- Spreje potřebné k převzetí
    reputationReward = 10,       -- Reputace za úspěšný sprej
}
```

### Reputační systém
```lua
Config.Reputation = {
    enabled = true,
    requiredForTakeover = 100,    -- Výchozí reputace potřebná k převzetí
    sprayReward = 10,             -- Reputace za sprej
    drugSaleReward = 5,           -- Reputace za prodej drogy
    territoryTakeoverReward = 50, -- Reputace za převzetí teritoria
    decayEnabled = true,          -- Povolit úbytek reputace
    decayAmount = 1,              -- Kolik reputace se ztratí za den
    maxReputation = 1000,         -- Maximální reputace
    -- Reputační požadavky podle typu teritoria
    territoryRequirements = {
        ['default'] = 100,        -- Výchozí požadavek
        ['high_value'] = 200,     -- Vysokohodnotná teritoria
        ['strategic'] = 150,      -- Strategická teritoria
        ['contested'] = 250,      -- Sporná teritoria
    }
}
```

### Gang Management
```lua
Config.GangManagement = {
    enabled = true,               -- Povolit gang management systém
    adminOnly = true,             -- Pouze admini mohou spravovat gangy
    maxMembers = 20,              -- Maximální počet členů na gang
    defaultRank = 'member',       -- Výchozí hodnost nového člena
}

Config.GangRanks = {
    ['leader'] = {
        name = 'Vůdce',
        level = 4,
        permissions = {'invite', 'kick', 'promote', 'demote', 'manage_territory'}
    },
    ['lieutenant'] = { name = 'Poručík', level = 3 },
    ['sergeant'] = { name = 'Seržant', level = 2 },
    ['member'] = { name = 'Člen', level = 1 }
}
```

### Drug Sales integrace
```lua
Config.DrugSales = {
    enabled = true,
    lationIntegration = true,     -- Integrace s Lation Drug Sales
    reputationPerSale = 5,        -- Reputace za prodej drogy
    bonusInOwnTerritory = 2,      -- Bonus za prodej ve vlastním teritoriu
    penaltyInEnemyTerritory = -1, -- Penalizace za prodej v nepřátelském teritoriu
}
```

## 🎮 Použití

### 👑 Admin Commands

#### Teritoria
- `/gangeditor create` - Vytvořit nové teritorium
- `/gangeditor edit [id]` - Upravit teritorium
- `/gangeditor delete [id]` - Smazat teritorium
- `/gangeditor list` - Seznam všech teritorií
- `/gang_assign [territory_id] [gang_name]` - Přiřadit teritorium gangu
- `/gang_territories [gang_name]` - Zobrazit teritoria konkrétního gangu

#### Spreje
- `/gang_clearsprays [territory_id]` - Smazat spreje z teritoria
- `/gang_clearsprays` - Smazat všechny spreje

#### Reputace
- `/gang_reputation add [player_id] [amount]` - Přidat reputaci hráči
- `/gang_reputation remove [player_id] [amount]` - Odebrat reputaci hráči
- `/gang_reputation check [player_id]` - Zkontrolovat reputaci hráče

#### Gang Management
- `/gang_create [gang_name] [gang_label]` - Vytvořit nový gang
- `/gang_delete [gang_name]` - Smazat gang
- `/gang_add [player_id] [gang_name] [rank]` - Přidat hráče do gangu
- `/gang_remove [player_id]` - Odebrat hráče z gangu
- `/gang_rank [player_id] [rank]` - Změnit hodnost hráče
- `/gang_info [gang_name]` - Zobrazit informace o gangu
- `/gang_list` - Seznam všech gangů

### 👥 Hráčské Commands

#### Základní
- `/gangmenu` (F6) - Otevřít hlavní gang menu
- `/territoryinfo` (F7) - Info o aktuálním teritoriu
- `/gangstats` - Statistiky gangu

#### Reputace
- `/reputation` (F8) - Otevřít reputační menu
- `/reputace` - Alternativní příkaz pro reputaci
- `/repstats` - Rychlé zobrazení reputace v chatu

#### Gang Management
- `/gangmanage` (F9) - Otevřít gang management menu
- `/mygang` - Zobrazit informace o svém gangu

#### Akce
- `E` (v teritoriu u stěny) - Sprejovat
- `Kolečko myši` (v editoru) - Změna poloměru teritoria

### Freecam Editor

**Ovládání:**
- **W/A/S/D** - Pohyb
- **Q/Z** - Nahoru/Dolů
- **Shift** - Rychlejší pohyb
- **Myš** - Rotace kamery
- **Kolečko myši** - Změna poloměru teritoria
- **E** - Potvrdit
- **X** - Zrušit

## 🎯 Reputační Systém

### Jak získat reputaci:
- **Sprejování**: +10 reputace za úspěšný sprej
- **Prodej drog**: +5 reputace za prodej (s Lation Drug Sales)
- **Bonus ve vlastním teritoriu**: +2 extra reputace za prodej drog
- **Převzetí teritoria**: +50 reputace pro všechny členy gangu
- **Penalizace**: -1 reputace za prodej v nepřátelském teritoriu

### Reputační hodnosti:
- **Nováček**: 0-19% max reputace
- **Začátečník**: 20-39% max reputace
- **Zkušený**: 40-59% max reputace
- **Respektovaný**: 60-79% max reputace
- **Veterán**: 80-89% max reputace
- **Legenda**: 90-100% max reputace

### Požadavky na převzetí teritoria:
- Minimálně **100 reputace** (konfigurovatelné)
- Požadovaný počet sprejů (výchozí: 3)
- Gang musí mít nejvíce sprejů v teritoriu

## 📊 Databázové tabulky

### gang_territories
- `id` - Unikátní ID teritoria
- `gang` - Název gangu vlastníka
- `coords` - JSON souřadnice středu
- `radius` - Poloměr teritoria
- `created_at` / `updated_at` - Časové značky

### gang_sprays
- `id` - Unikátní ID spreje
- `territory_id` - ID teritoria
- `gang` - Gang který sprej vytvořil
- `coords` - JSON souřadnice spreje
- `created_by` - Citizen ID tvůrce
- `created_at` - Čas vytvoření

### gang_reputation
- `id` - Unikátní ID záznamu
- `citizenid` - Citizen ID hráče
- `gang` - Název gangu
- `reputation` - Aktuální reputace
- `total_sprays` - Celkový počet sprejů
- `total_drug_sales` - Celkový počet prodejů drog
- `territories_taken` - Počet převzatých teritorií
- `last_activity` - Poslední aktivita
- `created_at` - Čas vytvoření

### gang_members
- `id` - Unikátní ID záznamu
- `citizenid` - Citizen ID hráče
- `gang` - Název gangu
- `rank` - Hodnost v gangu
- `joined_at` - Čas vstupu do gangu
- `last_seen` - Poslední aktivita

### gang_info
- `id` - Unikátní ID gangu
- `name` - Název gangu (unikátní)
- `label` - Zobrazovaný název gangu
- `leader` - Citizen ID vůdce
- `max_members` - Maximální počet členů
- `reputation_requirement` - Reputace potřebná k převzetí teritoria
- `territory_type` - Typ teritoria (default, high_value, strategic, contested)
- `created_at` / `updated_at` - Časové značky

## 🔧 Export funkce

### Client-side
```lua
-- Teritoria
local territoryId = exports['28_gang']:GetCurrentTerritory()
local territory = exports['28_gang']:GetTerritoryById(id)
local gang = exports['28_gang']:GetPlayerGang()
local inOwnTerritory = exports['28_gang']:IsPlayerInGangTerritory('ballas')

-- Reputace
local Reputation = exports['28_gang']:Reputation()
local currentRep = Reputation.GetCurrentReputation()
Reputation.RequestReputation()
Reputation.ShowReputationMenu()
```

### Server-side
```lua
-- Teritoria
local territories = exports['28_gang']:GetTerritories()
local territory = exports['28_gang']:GetTerritoryById(id)
local territoryId = exports['28_gang']:GetPlayerTerritory(source)
local inTerritory = exports['28_gang']:IsPlayerInGangTerritory(source, 'ballas')

-- Reputace
local success = exports['28_gang']:AddReputation(source, 10, 'custom_activity')
local reputation = exports['28_gang']:GetPlayerReputation(source)
local hasEnough = exports['28_gang']:HasEnoughReputation(source, 100)
```

## 🔄 Events

### Client Events
```lua
-- Reputace aktualizována
RegisterNetEvent('28_gang:reputationUpdated', function(newReputation)
    -- Vaše logika
end)

-- Hráč vstoupil do teritoria
RegisterNetEvent('28_gang:playerEnteredTerritory', function(territoryId)
    -- Vaše logika
end)
```

### Server Events
```lua
-- Reputace změněna
RegisterNetEvent('28_gang:reputationChanged', function(source, citizenid, gang, newRep, change, activity)
    -- Vaše logika
end)

-- Teritorium převzato
RegisterNetEvent('28_gang:territoryTaken', function(territoryId, newGang)
    -- Vaše logika
end)
```

## Přizpůsobení

### Přidání nového gangu
1. Přidejte gang do `Config.Gangs` v config.lua
2. Nastavte barvy, blip a název
3. Restart scriptu

### Změna animací
Upravte `Config.Animations` v config.lua:
```lua
Config.Animations = {
    spray = {
        dict = 'switch@franklin@lamar_tagging_wall',
        anim = 'lamar_tagging_wall_loop_lamar',
        flag = 16
    }
}
```

### Vlastní notifikace
Změňte `Config.Notifications.type` na 'custom' a upravte funkci v client/main.lua

## 🔧 Troubleshooting

### Časté problémy

1. **Teritoria se nenačítají**
   - Zkontrolujte databázové připojení
   - Ověřte, že jsou tabulky vytvořené
   - Restartujte script: `/restart 28_gang`

2. **Freecam nefunguje**
   - Zkontrolujte oprávnění v config.lua
   - Ověřte, že máte admin práva
   - Zkuste `/gangeditor create`

3. **Spreje se neukládají**
   - Zkontrolujte, že máte spray_can item
   - Ověřte databázové připojení
   - Zkontrolujte, že jste v teritoriu

4. **Reputace se neaktualizuje**
   - Zkontrolujte `Config.Reputation.enabled = true`
   - Ověřte, že jste v gangu (ne 'none')
   - Restartujte script

5. **Drug Sales integrace nefunguje**
   - Zkontrolujte, že máte Lation Drug Sales
   - Ověřte `Config.DrugSales.lationIntegration = true`
   - Zkontrolujte event name v Lation Drug Sales

6. **UI se nezobrazuje**
   - Zkontrolujte konzoli pro JavaScript chyby
   - Ověřte, že jsou HTML soubory načtené
   - Zkuste F5 pro refresh

### Debug režim

Zapněte `Config.Debug = true` pro detailní logy:
- `/gangdebug territories` - Seznam teritorií
- `/gangdebug current` - Aktuální teritorium
- `/gangdebug player` - Info o hráči
- `/gang_stats` - Statistiky scriptu
- `/gang_reload` - Reload dat z databáze

### Užitečné příkazy pro testování
```
/gang_reputation add [id] 100    # Přidat reputaci
/gang_assign 1 ballas           # Přiřadit teritorium
/gang_clearsprays               # Smazat všechny spreje
```

## Podpora

Pro podporu a hlášení chyb kontaktujte vývojáře nebo vytvořte issue.

## 📋 Detailní Instrukce k Používání

### Pro Administrátory

#### 1. Vytvoření gangu a přidání členů
```
1. Vytvořte gang: /gang_create ballas "The Ballas Gang"
2. Přidejte hráče: /gang_add 1 ballas leader
3. Přidejte další členy: /gang_add 2 ballas member
4. Zkontrolujte info: /gang_info ballas
```

#### 2. Vytvoření prvního teritoria
```
1. Zadejte: /gangeditor create
2. Použijte WASD pro pohyb, myš pro rotaci
3. Kolečko myši pro změnu poloměru
4. E pro potvrzení, X pro zrušení
5. Přiřaďte gang: /gang_assign 1 ballas
```

#### 3. Nastavení reputačních požadavků
```
- Upravte Config.Reputation.territoryRequirements
- Nebo nastavte specificky pro gang v databázi
- Použijte různé typy teritorií (default, high_value, strategic, contested)
```

#### 3. Monitoring reputace
```
- /gang_reputation check [player_id] - Zkontrolovat reputaci
- Sledujte logy pro debug informace
- Používejte /gang_stats pro přehled
```

### Pro Hráče

#### 1. Vstup do gangu
```
- Musíte být přiřazeni do gangu přes QBCore
- Gang nesmí být 'none'
- Zkontrolujte: /repstats
```

#### 2. Sprejování teritorií
```
1. Jděte do nepřátelského teritoria
2. Najděte stěnu nebo objekt
3. Mějte spray_can v inventáři
4. Stiskněte E u stěny
5. Počkejte 10 sekund (konfigurovatelné)
6. Získáte reputaci za úspěšný sprej
```

#### 3. Převzetí teritoria
```
- Potřebujete minimálně 100 reputace (konfigurovatelné)
- Musíte mít nejvíce sprejů v teritoriu
- Potřebujete alespoň 3 spreje (konfigurovatelné)
- Všichni členové gangu získají bonus reputaci
```

#### 4. Získávání reputace
```
- Sprejování: +10 reputace
- Prodej drog (s Lation): +5 reputace
- Bonus ve vlastním teritoriu: +2 extra
- Převzetí teritoria: +50 pro všechny
- Penalizace v nepřátelském: -1
```

### Tipy a Triky

#### Pro maximální efektivitu:
1. **Koordinujte s gangem** - sprejujte společně
2. **Prodávejte drogy ve vlastních teritoriích** - bonus reputace
3. **Sledujte reputační žebříček** - F8 menu
4. **Bráňte svá teritoria** - sledujte nepřátelské spreje
5. **Používejte strategii** - cílejte na slabší teritoria

#### Časté chyby:
- ❌ Sprejování bez spray_can
- ❌ Pokus o převzetí bez dostatečné reputace
- ❌ Sprejování ve vlastním teritoriu
- ❌ Ignorování cooldownu mezi spreji

## 🎮 Herní Mechaniky

### Reputační Decay
- Reputace klesá o 1 bod denně při neaktivitě
- Lze vypnout v config.lua
- Motivuje k pravidelné aktivitě

### Teritoriální Kontrola
- Gang s nejvíce spreji kontroluje teritorium
- Minimální reputace je nutná pro převzetí
- Vizuální indikace vlastnictví přes blipy

### Drug Sales Bonus
- Integrace s Lation Drug Sales
- Automatická detekce prodejů
- Bonus/penalizace podle teritoria

## 📞 Podpora

Pro podporu a hlášení chyb:
1. Zkontrolujte tento README
2. Zapněte debug režim
3. Zkontrolujte server logy
4. Kontaktujte vývojáře s detailními informacemi

## 📄 Licence

Tento script je vytvořen pro DVRP server. Všechna práva vyhrazena.

---

**Verze**: 1.0.0 s Reputačním Systémem
**Autor**: DVRP Development Team
**Poslední aktualizace**: 2024
