-- Globální proměnné
local territories = {}
local sprays = {}
local playerTerritories = {} -- Sledování hráčů v teritoriích

-- Inicializace
CreateThread(function()
    -- Počkat na načtení databáze
    Wait(2000)

    -- Inicializovat databázi (volitelné - tabulky jsou už vytvořené přes SQL)
    print('[28_gang] Database module loaded - using imported SQL tables')

    -- Načíst data z databáze
    local Database = exports[GetCurrentResourceName()]:Database()
    territories = Database.GetAllTerritories()
    sprays = Database.GetAllSprays()

    local territoryCount = 0
    local sprayCount = 0
    for _ in pairs(territories) do territoryCount = territoryCount + 1 end
    for _ in pairs(sprays) do sprayCount = sprayCount + 1 end

    print('[28_gang] Server initialized with ' .. territoryCount .. ' territories and ' .. sprayCount .. ' sprays')
end)

-- Utility funkce
function HasPermission(source, permission)
    if permission == 'admin' then
        return exports.qbx_core:HasPermission(source, 'admin')
    end
    return true
end

function GetPlayerGang(source)
    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return nil end

    return Player.PlayerData.gang and Player.PlayerData.gang.name ~= 'none' and Player.PlayerData.gang.name or nil
end

function NotifyPlayer(source, type, message)
    TriggerClientEvent('28_gang:notification', source, type, message)
end

function NotifyAllPlayers(type, message)
    TriggerClientEvent('28_gang:notification', -1, type, message)
end

-- Territory management
RegisterNetEvent('28_gang:requestTerritories', function()
    local source = source
    TriggerClientEvent('28_gang:territoriesLoaded', source, territories)
    TriggerClientEvent('28_gang:spraysLoaded', source, sprays)
end)

RegisterNetEvent('28_gang:createTerritory', function(coords, radius)
    local source = source

    if not HasPermission(source, Config.Editor.permission) then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    -- Kontrola minimální vzdálenosti
    for _, territory in pairs(territories) do
        if territory.coords then
            local distance = #(vector3(coords.x, coords.y, coords.z) - vector3(territory.coords.x, territory.coords.y, territory.coords.z))
            if distance < Config.Territories.minDistance then
                NotifyPlayer(source, 'error', 'Teritorium je příliš blízko k jinému teritoriu!')
                return
            end
        end
    end
    
    -- Kontrola maximálního počtu teritorií
    local territoryCount = 0
    for _ in pairs(territories) do
        territoryCount = territoryCount + 1
    end
    
    if territoryCount >= Config.Territories.maxTerritories then
        NotifyPlayer(source, 'error', 'Dosažen maximální počet teritorií!')
        return
    end
    
    -- Vytvořit teritorium
    local Database = exports[GetCurrentResourceName()]:Database()
    local territoryId = Database.CreateTerritory('none', coords, radius)
    
    if territoryId then
        territories[territoryId] = {
            id = territoryId,
            gang = 'none',
            coords = coords,
            radius = radius
        }
        
        TriggerClientEvent('28_gang:territoryCreated', -1, territoryId, territories[territoryId])
        
        if Config.Debug then
            print('[28_gang] Territory created with ID: ' .. territoryId)
        end
    else
        NotifyPlayer(source, 'error', 'Chyba při vytváření teritoria!')
    end
end)

RegisterNetEvent('28_gang:updateTerritory', function(territoryId, coords, radius)
    local source = source
    
    if not HasPermission(source, Config.Editor.permission) then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Teritorium neexistuje!')
        return
    end
    
    -- Aktualizovat teritorium
    local Database = exports[GetCurrentResourceName()]:Database()
    local success = Database.UpdateTerritory(territoryId, coords, radius)
    
    if success then
        territories[territoryId].coords = coords
        territories[territoryId].radius = radius
        
        TriggerClientEvent('28_gang:territoryUpdated', -1, territoryId, territories[territoryId])
        
        if Config.Debug then
            print('[28_gang] Territory updated: ' .. territoryId)
        end
    else
        NotifyPlayer(source, 'error', 'Chyba při aktualizaci teritoria!')
    end
end)

RegisterNetEvent('28_gang:deleteTerritory', function(territoryId)
    local source = source
    
    if not HasPermission(source, Config.Editor.permission) then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    if not territories[territoryId] then
        NotifyPlayer(source, 'error', 'Teritorium neexistuje!')
        return
    end
    
    -- Smazat teritorium a všechny spreje
    local Database = exports[GetCurrentResourceName()]:Database()
    local success = Database.DeleteTerritory(territoryId)
    
    if success then
        -- Smazat spreje z paměti
        for sprayId, spray in pairs(sprays) do
            if spray.territory_id == territoryId then
                sprays[sprayId] = nil
            end
        end
        
        territories[territoryId] = nil
        
        TriggerClientEvent('28_gang:territoryDeleted', -1, territoryId)
        
        if Config.Debug then
            print('[28_gang] Territory deleted: ' .. territoryId)
        end
    else
        NotifyPlayer(source, 'error', 'Chyba při mazání teritoria!')
    end
end)

RegisterNetEvent('28_gang:listTerritories', function()
    local source = source
    
    if not HasPermission(source, Config.Editor.permission) then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local message = '^3=== GANG TERRITORIES ===^7\n'
    for id, territory in pairs(territories) do
        local gangName = territory.gang == 'none' and 'Žádný' or (Config.Gangs[territory.gang] and Config.Gangs[territory.gang].name or territory.gang)
        message = message .. string.format('^2ID: %s^7 | ^1Gang: %s^7 | ^4Radius: %.1fm^7\n', id, gangName, territory.radius)
    end
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {255, 255, 255},
        multiline = true,
        args = {"Gang System", message}
    })
end)

-- Player territory tracking
RegisterNetEvent('28_gang:playerTerritoryChanged', function(territoryId)
    local source = source
    playerTerritories[source] = territoryId
    
    if territoryId then
        TriggerClientEvent('28_gang:playerEnteredTerritory', source, territoryId)
    end
end)

-- Cleanup při odpojení hráče
AddEventHandler('playerDropped', function()
    local source = source
    playerTerritories[source] = nil
end)

-- Export funkcí
exports('GetTerritories', function()
    return territories
end)

exports('GetTerritoryById', function(id)
    return territories[id]
end)

exports('GetPlayerTerritory', function(source)
    return playerTerritories[source]
end)

exports('IsPlayerInGangTerritory', function(source, gang)
    local territoryId = playerTerritories[source]
    if not territoryId or not territories[territoryId] then return false end
    
    return territories[territoryId].gang == gang
end)

-- Debug commands
if Config.Debug then
    RegisterCommand('gang_reload', function(source, args, rawCommand)
        if source == 0 or HasPermission(source, 'admin') then
            local Database = exports[GetCurrentResourceName()]:Database()
            territories = Database.GetAllTerritories()
            sprays = Database.GetAllSprays()
            
            TriggerClientEvent('28_gang:territoriesLoaded', -1, territories)
            TriggerClientEvent('28_gang:spraysLoaded', -1, sprays)
            
            print('[28_gang] Data reloaded from database')
            if source ~= 0 then
                NotifyPlayer(source, 'success', 'Gang data reloaded!')
            end
        end
    end, false)
    
    RegisterCommand('gang_stats', function(source, args, rawCommand)
        if source == 0 or HasPermission(source, 'admin') then
            local territoryCount = 0
            local sprayCount = 0
            
            for _ in pairs(territories) do territoryCount = territoryCount + 1 end
            for _ in pairs(sprays) do sprayCount = sprayCount + 1 end
            
            local message = string.format('Territories: %d | Sprays: %d | Players in territories: %d', 
                territoryCount, sprayCount, #playerTerritories)
            
            if source == 0 then
                print('[28_gang] ' .. message)
            else
                NotifyPlayer(source, 'info', message)
            end
        end
    end, false)
end
