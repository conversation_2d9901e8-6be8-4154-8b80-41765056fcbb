local Reputation = {}
-- QBX Core je dostupný přes exports.qbx_core
local currentReputation = 0
local gangLeaderboard = {}
local topGangs = {}

function Reputation.GetCurrentReputation()
    return currentReputation
end

function Reputation.RequestReputation()
    TriggerServerEvent('28_gang:requestReputation')
end

function Reputation.RequestGangLeaderboard()
    TriggerServerEvent('28_gang:requestGangLeaderboard')
end

function Reputation.RequestTopGangs()
    TriggerServerEvent('28_gang:requestTopGangs')
end

function Reputation.ShowReputationHUD()
    if not Config.Reputation.enabled then return end

    local PlayerData = exports.qbx_core.PlayerData
    if not PlayerData.gang or PlayerData.gang.name == 'none' then return end
    
    -- Zobrazit reputaci v HUD
    local reputationText = string.format('Reputace: %d/%d', currentReputation, Config.Reputation.maxReputation)
    
    SetTextFont(Config.UI.textFont)
    SetTextScale(0.25, 0.25)
    SetTextColour(255, 215, 0, 255) -- Zlatá barva
    SetTextOutline()
    SetTextEntry('STRING')
    AddTextComponentString(reputationText)
    DrawText(0.02, 0.06) -- Pod gang HUD
end

function Reputation.ShowReputationMenu()
    local PlayerData = exports.qbx_core.PlayerData
    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return
    end
    
    -- Požádat o aktuální data
    Reputation.RequestReputation()
    Reputation.RequestGangLeaderboard()
    Reputation.RequestTopGangs()
    
    -- Otevřít menu po krátké pauze
    CreateThread(function()
        Wait(500)
        local UI = exports[GetCurrentResourceName()]:UI()
        if UI then
            UI.OpenMenu('reputationMenu', {
                currentReputation = currentReputation,
                maxReputation = Config.Reputation.maxReputation,
                leaderboard = gangLeaderboard,
                topGangs = topGangs,
                gang = PlayerData.gang
            })
        end
    end)
end

function Reputation.GetReputationColor(reputation)
    local maxRep = Config.Reputation.maxReputation
    local percentage = reputation / maxRep
    
    if percentage >= 0.8 then
        return {r = 255, g = 215, b = 0} -- Zlatá
    elseif percentage >= 0.6 then
        return {r = 138, g = 43, b = 226} -- Fialová
    elseif percentage >= 0.4 then
        return {r = 0, g = 191, b = 255} -- Modrá
    elseif percentage >= 0.2 then
        return {r = 50, g = 205, b = 50} -- Zelená
    else
        return {r = 169, g = 169, b = 169} -- Šedá
    end
end

function Reputation.GetReputationRank(reputation)
    local maxRep = Config.Reputation.maxReputation
    local percentage = reputation / maxRep
    
    if percentage >= 0.9 then
        return 'Legenda'
    elseif percentage >= 0.8 then
        return 'Veterán'
    elseif percentage >= 0.6 then
        return 'Respektovaný'
    elseif percentage >= 0.4 then
        return 'Zkušený'
    elseif percentage >= 0.2 then
        return 'Začátečník'
    else
        return 'Nováček'
    end
end

-- Event handlers
RegisterNetEvent('28_gang:reputationReceived', function(reputation)
    currentReputation = reputation
end)

RegisterNetEvent('28_gang:reputationUpdated', function(newReputation)
    local oldReputation = currentReputation
    currentReputation = newReputation
    
    -- Zobrazit změnu reputace
    if newReputation > oldReputation then
        local gained = newReputation - oldReputation
        TriggerEvent('28_gang:notification', 'success', string.format('+%d Reputace!', gained))
    elseif newReputation < oldReputation then
        local lost = oldReputation - newReputation
        TriggerEvent('28_gang:notification', 'error', string.format('-%d Reputace!', lost))
    end
end)

RegisterNetEvent('28_gang:gangLeaderboardReceived', function(leaderboard)
    gangLeaderboard = leaderboard
end)

RegisterNetEvent('28_gang:topGangsReceived', function(gangs)
    topGangs = gangs
end)

-- Commands
RegisterCommand('reputation', function()
    Reputation.ShowReputationMenu()
end, false)

RegisterCommand('reputace', function()
    Reputation.ShowReputationMenu()
end, false)

RegisterCommand('repstats', function()
    local PlayerData = exports.qbx_core.PlayerData
    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        TriggerEvent('28_gang:notification', 'error', 'Nejsi v žádném gangu!')
        return
    end
    
    local rank = Reputation.GetReputationRank(currentReputation)
    local color = Reputation.GetReputationColor(currentReputation)
    
    TriggerEvent('chat:addMessage', {
        color = {color.r, color.g, color.b},
        multiline = true,
        args = {"Reputace", string.format('Tvoje reputace: %d/%d\nHodnost: %s\nGang: %s', 
            currentReputation, Config.Reputation.maxReputation, rank, PlayerData.gang.label or PlayerData.gang.name)}
    })
end, false)

-- Key mapping
RegisterKeyMapping('reputation', 'Gang Reputation Menu', 'keyboard', 'F8')

-- Inicializace při načtení
CreateThread(function()
    while not exports.qbx_core do
        Wait(100)
    end

    -- Počkat na načtení hráče
    while not exports.qbx_core.PlayerData.citizenid do
        Wait(1000)
    end

    -- Načíst reputaci
    Reputation.RequestReputation()
end)

-- HUD thread
if Config.UI and Config.UI.showReputationHUD then
    CreateThread(function()
        while true do
            if Config.Reputation.enabled then
                Reputation.ShowReputationHUD()
            end
            Wait(0)
        end
    end)
end

-- Automatické obnovení reputace každých 5 minut
CreateThread(function()
    while true do
        Wait(300000) -- 5 minut
        Reputation.RequestReputation()
    end
end)

-- Export pro ostatní moduly
exports('Reputation', Reputation)
return Reputation
