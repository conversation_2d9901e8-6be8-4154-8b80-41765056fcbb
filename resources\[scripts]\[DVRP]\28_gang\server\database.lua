local Database = {}

-- Datab<PERSON>ze je inicializována přes SQL soubor
function Database.Init()
    print('[28_gang] Database module loaded - using imported SQL tables')
end

-- Territory funkce
function Database.GetAllTerritories()
    local result = MySQL.query.await('SELECT * FROM gang_territories')
    local territories = {}
    
    for _, row in pairs(result) do
        territories[row.id] = {
            id = row.id,
            gang = row.gang,
            coords = json.decode(row.coords),
            radius = row.radius,
            created_at = row.created_at,
            updated_at = row.updated_at
        }
    end
    
    return territories
end

function Database.GetTerritoryById(id)
    local result = MySQL.query.await('SELECT * FROM gang_territories WHERE id = ?', {id})
    
    if result and result[1] then
        local row = result[1]
        return {
            id = row.id,
            gang = row.gang,
            coords = json.decode(row.coords),
            radius = row.radius,
            created_at = row.created_at,
            updated_at = row.updated_at
        }
    end
    
    return nil
end

function Database.CreateTerritory(gang, coords, radius)
    local result = MySQL.insert.await('INSERT INTO gang_territories (gang, coords, radius) VALUES (?, ?, ?)', {
        gang,
        json.encode(coords),
        radius
    })
    
    return result
end

function Database.UpdateTerritory(id, coords, radius)
    local result = MySQL.update.await('UPDATE gang_territories SET coords = ?, radius = ? WHERE id = ?', {
        json.encode(coords),
        radius,
        id
    })
    
    return result
end

function Database.DeleteTerritory(id)
    local result = MySQL.query.await('DELETE FROM gang_territories WHERE id = ?', {id})
    return result
end

function Database.UpdateTerritoryGang(id, gang)
    local result = MySQL.update.await('UPDATE gang_territories SET gang = ? WHERE id = ?', {
        gang,
        id
    })
    
    return result
end

-- Spray funkce
function Database.GetSpraysByTerritory(territoryId)
    local result = MySQL.query.await('SELECT * FROM gang_sprays WHERE territory_id = ?', {territoryId})
    local sprays = {}
    
    for _, row in pairs(result) do
        sprays[row.id] = {
            id = row.id,
            territory_id = row.territory_id,
            gang = row.gang,
            coords = json.decode(row.coords),
            created_by = row.created_by,
            created_at = row.created_at
        }
    end
    
    return sprays
end

function Database.GetAllSprays()
    local result = MySQL.query.await('SELECT * FROM gang_sprays')
    local sprays = {}
    
    for _, row in pairs(result) do
        sprays[row.id] = {
            id = row.id,
            territory_id = row.territory_id,
            gang = row.gang,
            coords = json.decode(row.coords),
            created_by = row.created_by,
            created_at = row.created_at
        }
    end
    
    return sprays
end

function Database.CreateSpray(territoryId, gang, coords, createdBy)
    local result = MySQL.insert.await('INSERT INTO gang_sprays (territory_id, gang, coords, created_by) VALUES (?, ?, ?, ?)', {
        territoryId,
        gang,
        json.encode(coords),
        createdBy
    })
    
    return result
end

function Database.DeleteSpray(id)
    local result = MySQL.query.await('DELETE FROM gang_sprays WHERE id = ?', {id})
    return result
end

function Database.DeleteSpraysByTerritory(territoryId)
    local result = MySQL.query.await('DELETE FROM gang_sprays WHERE territory_id = ?', {territoryId})
    return result
end

function Database.CountSpraysByGangInTerritory(territoryId, gang)
    local result = MySQL.scalar.await('SELECT COUNT(*) FROM gang_sprays WHERE territory_id = ? AND gang = ?', {
        territoryId,
        gang
    })
    
    return result or 0
end

-- Statistiky
function Database.GetGangStats(gang)
    local territories = MySQL.scalar.await('SELECT COUNT(*) FROM gang_territories WHERE gang = ?', {gang}) or 0
    local sprays = MySQL.scalar.await('SELECT COUNT(*) FROM gang_sprays WHERE gang = ?', {gang}) or 0
    
    return {
        territories = territories,
        sprays = sprays
    }
end

function Database.GetTerritoryStats(territoryId)
    local result = MySQL.query.await([[
        SELECT gang, COUNT(*) as count 
        FROM gang_sprays 
        WHERE territory_id = ? 
        GROUP BY gang
    ]], {territoryId})
    
    local stats = {}
    for _, row in pairs(result) do
        stats[row.gang] = row.count
    end
    
    return stats
end

-- Cleanup funkce
function Database.CleanupOldSprays(days)
    days = days or 30
    local result = MySQL.query.await('DELETE FROM gang_sprays WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)', {days})
    return result
end

-- Reputation funkce
function Database.GetPlayerReputation(citizenid, gang)
    local result = MySQL.query.await('SELECT * FROM gang_reputation WHERE citizenid = ? AND gang = ?', {
        citizenid, gang
    })

    if result and result[1] then
        return result[1]
    end

    return nil
end

function Database.CreatePlayerReputation(citizenid, gang)
    local result = MySQL.insert.await('INSERT INTO gang_reputation (citizenid, gang, reputation) VALUES (?, ?, ?)', {
        citizenid, gang, 0
    })

    return result
end

function Database.UpdatePlayerReputation(citizenid, gang, reputation, activity_type)
    local current = Database.GetPlayerReputation(citizenid, gang)

    if not current then
        Database.CreatePlayerReputation(citizenid, gang)
        current = { reputation = 0, total_sprays = 0, total_drug_sales = 0, territories_taken = 0 }
    end

    local newReputation = math.max(Config.Reputation.minReputation,
                         math.min(Config.Reputation.maxReputation, current.reputation + reputation))

    local updateQuery = 'UPDATE gang_reputation SET reputation = ?, last_activity = NOW()'
    local params = { newReputation }

    -- Aktualizovat statistiky podle typu aktivity
    if activity_type == 'spray' then
        updateQuery = updateQuery .. ', total_sprays = total_sprays + 1'
    elseif activity_type == 'drug_sale' then
        updateQuery = updateQuery .. ', total_drug_sales = total_drug_sales + 1'
    elseif activity_type == 'territory_taken' then
        updateQuery = updateQuery .. ', territories_taken = territories_taken + 1'
    end

    updateQuery = updateQuery .. ' WHERE citizenid = ? AND gang = ?'
    table.insert(params, citizenid)
    table.insert(params, gang)

    MySQL.update.await(updateQuery, params)

    return newReputation
end

function Database.GetGangReputationLeaderboard(gang, limit)
    limit = limit or 10
    local result = MySQL.query.await([[
        SELECT citizenid, reputation, total_sprays, total_drug_sales, territories_taken, last_activity
        FROM gang_reputation
        WHERE gang = ?
        ORDER BY reputation DESC
        LIMIT ?
    ]], { gang, limit })

    return result or {}
end

function Database.GetTopGangs(limit)
    limit = limit or 5
    local result = MySQL.query.await([[
        SELECT gang,
               SUM(reputation) as total_reputation,
               COUNT(*) as member_count,
               AVG(reputation) as avg_reputation,
               SUM(total_sprays) as total_sprays,
               SUM(total_drug_sales) as total_drug_sales,
               SUM(territories_taken) as total_territories_taken
        FROM gang_reputation
        GROUP BY gang
        ORDER BY total_reputation DESC
        LIMIT ?
    ]], { limit })

    return result or {}
end

function Database.DecayReputation(days)
    days = days or 1
    local decayAmount = Config.Reputation.decayAmount * days

    local result = MySQL.update.await([[
        UPDATE gang_reputation
        SET reputation = GREATEST(?, reputation - ?)
        WHERE last_activity < DATE_SUB(NOW(), INTERVAL ? DAY)
    ]], { Config.Reputation.minReputation, decayAmount, days })

    return result
end

function Database.CleanupInactiveReputation(days)
    days = days or 90
    local result = MySQL.query.await([[
        DELETE FROM gang_reputation
        WHERE last_activity < DATE_SUB(NOW(), INTERVAL ? DAY)
        AND reputation <= ?
    ]], { days, Config.Reputation.minReputation })

    return result
end

-- Gang Management funkce
function Database.CreateGang(name, label, leader, maxMembers, reputationRequirement, territoryType)
    maxMembers = maxMembers or Config.GangManagement.maxMembers
    reputationRequirement = reputationRequirement or Config.Reputation.requiredForTakeover
    territoryType = territoryType or 'default'

    local result = MySQL.insert.await([[
        INSERT INTO gang_info (name, label, leader, max_members, reputation_requirement, territory_type)
        VALUES (?, ?, ?, ?, ?, ?)
    ]], { name, label, leader, maxMembers, reputationRequirement, territoryType })

    return result
end

function Database.DeleteGang(gangName)
    -- Smazat všechny členy gangu
    MySQL.query.await('DELETE FROM gang_members WHERE gang = ?', { gangName })

    -- Smazat gang info
    local result = MySQL.query.await('DELETE FROM gang_info WHERE name = ?', { gangName })

    return result
end

function Database.GetGangInfo(gangName)
    local result = MySQL.query.await('SELECT * FROM gang_info WHERE name = ?', { gangName })

    if result and result[1] then
        return result[1]
    end

    return nil
end

function Database.GetAllGangs()
    local result = MySQL.query.await('SELECT * FROM gang_info ORDER BY name')
    return result or {}
end

function Database.AddPlayerToGang(citizenid, gang, rank)
    rank = rank or Config.GangManagement.defaultRank

    local result = MySQL.insert.await([[
        INSERT INTO gang_members (citizenid, gang, rank)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE gang = VALUES(gang), rank = VALUES(rank), last_seen = NOW()
    ]], { citizenid, gang, rank })

    return result
end

function Database.RemovePlayerFromGang(citizenid)
    local result = MySQL.query.await('DELETE FROM gang_members WHERE citizenid = ?', { citizenid })
    return result
end

function Database.GetPlayerGangInfo(citizenid)
    local result = MySQL.query.await([[
        SELECT gm.*, gi.label, gi.max_members, gi.reputation_requirement
        FROM gang_members gm
        LEFT JOIN gang_info gi ON gm.gang = gi.name
        WHERE gm.citizenid = ?
    ]], { citizenid })

    if result and result[1] then
        return result[1]
    end

    return nil
end

function Database.GetGangMembers(gangName)
    local result = MySQL.query.await([[
        SELECT gm.*, p.charinfo
        FROM gang_members gm
        LEFT JOIN players p ON gm.citizenid = p.citizenid
        WHERE gm.gang = ?
        ORDER BY
            CASE gm.rank
                WHEN 'leader' THEN 1
                WHEN 'lieutenant' THEN 2
                WHEN 'sergeant' THEN 3
                WHEN 'member' THEN 4
                ELSE 5
            END, gm.joined_at
    ]], { gangName })

    return result or {}
end

function Database.UpdatePlayerRank(citizenid, rank)
    local result = MySQL.update.await('UPDATE gang_members SET rank = ? WHERE citizenid = ?', { rank, citizenid })
    return result
end

function Database.GetGangMemberCount(gangName)
    local result = MySQL.scalar.await('SELECT COUNT(*) FROM gang_members WHERE gang = ?', { gangName })
    return result or 0
end

function Database.UpdateGangLeader(gangName, newLeader)
    local result = MySQL.update.await('UPDATE gang_info SET leader = ? WHERE name = ?', { newLeader, gangName })
    return result
end

function Database.GetGangReputationRequirement(gangName)
    local result = MySQL.scalar.await('SELECT reputation_requirement FROM gang_info WHERE name = ?', { gangName })

    if result then
        return result
    end

    -- Fallback na config
    if Config.Gangs[gangName] and Config.Gangs[gangName].reputationRequirement then
        return Config.Gangs[gangName].reputationRequirement
    end

    return Config.Reputation.requiredForTakeover
end

function Database.UpdateGangSettings(gangName, settings)
    local updateFields = {}
    local params = {}

    if settings.label then
        table.insert(updateFields, 'label = ?')
        table.insert(params, settings.label)
    end

    if settings.max_members then
        table.insert(updateFields, 'max_members = ?')
        table.insert(params, settings.max_members)
    end

    if settings.reputation_requirement then
        table.insert(updateFields, 'reputation_requirement = ?')
        table.insert(params, settings.reputation_requirement)
    end

    if settings.territory_type then
        table.insert(updateFields, 'territory_type = ?')
        table.insert(params, settings.territory_type)
    end

    if #updateFields > 0 then
        table.insert(params, gangName)
        local query = 'UPDATE gang_info SET ' .. table.concat(updateFields, ', ') .. ' WHERE name = ?'
        local result = MySQL.update.await(query, params)
        return result
    end

    return false
end

-- Export pro ostatní moduly
exports('Database', Database)
return Database
