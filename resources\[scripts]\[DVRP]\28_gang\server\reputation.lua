-- Utility funkce
function GetPlayerGang(source)
    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return nil end

    return Player.PlayerData.gang and Player.PlayerData.gang.name ~= 'none' and Player.PlayerData.gang.name or nil
end

function NotifyPlayer(source, type, message)
    TriggerClientEvent('28_gang:notification', source, type, message)
end

function NotifyGangMembers(gang, type, message)
    local Players = exports.qbx_core:GetQBPlayers()
    for _, playerId in pairs(Players) do
        local Player = exports.qbx_core:GetPlayer(playerId)
        if Player and Player.PlayerData.gang and Player.PlayerData.gang.name == gang then
            TriggerClientEvent('28_gang:notification', playerId, type, message)
        end
    end
end

-- Reputační funkce
function AddReputation(source, amount, activity_type, silent)
    if not Config.Reputation.enabled then return false end

    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return false end

    local gang = GetPlayerGang(source)
    if not gang then return false end

    local Database = exports[GetCurrentResourceName()]:Database()
    local newReputation = Database.UpdatePlayerReputation(Player.PlayerData.citizenid, gang, amount, activity_type)
    
    if not silent and amount > 0 then
        NotifyPlayer(source, 'success', string.format(Config.Texts[Config.Locale]['reputation_gained'], amount))
    elseif not silent and amount < 0 then
        NotifyPlayer(source, 'error', string.format(Config.Texts[Config.Locale]['reputation_lost'], math.abs(amount)))
    end
    
    -- Trigger event pro ostatní scripty
    TriggerEvent('28_gang:reputationChanged', source, Player.PlayerData.citizenid, gang, newReputation, amount, activity_type)
    
    if Config.Debug then
        print(string.format('[28_gang] Player %s (%s) gained %d reputation for %s. New total: %d', 
            Player.PlayerData.name, gang, amount, activity_type or 'unknown', newReputation))
    end
    
    return true, newReputation
end

function GetPlayerReputation(source)
    local Player = exports.qbx_core:GetPlayer(source)
    if not Player then return 0 end

    local gang = GetPlayerGang(source)
    if not gang then return 0 end

    local Database = exports[GetCurrentResourceName()]:Database()
    local reputationData = Database.GetPlayerReputation(Player.PlayerData.citizenid, gang)
    return reputationData and reputationData.reputation or 0
end

function HasEnoughReputation(source, required)
    local currentReputation = GetPlayerReputation(source)
    return currentReputation >= required
end

-- Event handlers
RegisterNetEvent('28_gang:sprayCompleted', function(territoryId)
    local source = source
    
    if not Config.Reputation.enabled then return end
    
    local success, newReputation = AddReputation(source, Config.Reputation.sprayReward, 'spray')
    
    if success then
        TriggerClientEvent('28_gang:reputationUpdated', source, newReputation)
    end
end)

RegisterNetEvent('28_gang:territoryTaken', function(territoryId, gang)
    if not Config.Reputation.enabled then return end
    
    -- Přidat reputaci všem členům gangu, kteří jsou online
    local Players = exports.qbx_core:GetPlayers()
    for _, playerId in pairs(Players) do
        local Player = exports.qbx_core:GetPlayer(playerId)
        if Player and Player.PlayerData.gang and Player.PlayerData.gang.name == gang then
            local success, newReputation = AddReputation(playerId, Config.Reputation.territoryTakeoverReward, 'territory_taken')
            if success then
                TriggerClientEvent('28_gang:reputationUpdated', playerId, newReputation)
                NotifyPlayer(playerId, 'success', string.format(Config.Texts[Config.Locale]['territory_takeover_reputation'], Config.Reputation.territoryTakeoverReward))
            end
        end
    end
end)

-- Lation Drug Sales integrace
if Config.DrugSales.enabled and Config.DrugSales.lationIntegration then
    -- Event handler pro Lation Drug Sales
    RegisterNetEvent('lation_drugsales:drugSold', function(drugData)
        local source = source
        
        if not Config.Reputation.enabled then return end
        
        local Player = exports.qbx_core:GetPlayer(source)
        if not Player then return end
        
        local gang = GetPlayerGang(source)
        if not gang then return end
        
        -- Získat aktuální teritorium hráče
        local currentTerritory = exports[GetCurrentResourceName()]:GetPlayerTerritory(source)
        local reputationReward = Config.DrugSales.reputationPerSale
        
        if currentTerritory then
            local territory = exports[GetCurrentResourceName()]:GetTerritoryById(currentTerritory)
            
            if territory then
                if territory.gang == gang then
                    -- Bonus za prodej ve vlastním teritoriu
                    reputationReward = reputationReward + Config.DrugSales.bonusInOwnTerritory
                elseif territory.gang ~= 'none' then
                    -- Penalizace za prodej v nepřátelském teritoriu
                    reputationReward = reputationReward + Config.DrugSales.penaltyInEnemyTerritory
                end
            end
        end
        
        if reputationReward > 0 then
            local success, newReputation = AddReputation(source, reputationReward, 'drug_sale')
            if success then
                TriggerClientEvent('28_gang:reputationUpdated', source, newReputation)
                NotifyPlayer(source, 'info', string.format(Config.Texts[Config.Locale]['drug_sale_reputation'], reputationReward))
            end
        end
        
        if Config.Debug then
            print(string.format('[28_gang] Drug sale by %s in territory %s (owner: %s), reputation reward: %d', 
                Player.PlayerData.name, currentTerritory or 'none', 
                currentTerritory and exports[GetCurrentResourceName()]:GetTerritoryById(currentTerritory).gang or 'none', 
                reputationReward))
        end
    end)
end

-- API Events
RegisterNetEvent('28_gang:requestReputation', function()
    local source = source
    local reputation = GetPlayerReputation(source)
    TriggerClientEvent('28_gang:reputationReceived', source, reputation)
end)

RegisterNetEvent('28_gang:requestGangLeaderboard', function()
    local source = source
    local gang = GetPlayerGang(source)
    
    if not gang then
        NotifyPlayer(source, 'error', 'Nejsi v žádném gangu!')
        return
    end

    local Database = exports[GetCurrentResourceName()]:Database()
    local leaderboard = Database.GetGangReputationLeaderboard(gang, 10)
    TriggerClientEvent('28_gang:gangLeaderboardReceived', source, leaderboard)
end)

RegisterNetEvent('28_gang:requestTopGangs', function()
    local source = source
    local Database = exports[GetCurrentResourceName()]:Database()
    local topGangs = Database.GetTopGangs(5)
    TriggerClientEvent('28_gang:topGangsReceived', source, topGangs)
end)

-- Admin commands
RegisterCommand('gang_reputation', function(source, args, rawCommand)
    if source ~= 0 and not exports.qbx_core:HasPermission(source, 'admin') then
        NotifyPlayer(source, 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local action = args[1]
    local targetId = tonumber(args[2])
    local amount = tonumber(args[3])
    
    if action == 'add' and targetId and amount then
        local success, newReputation = AddReputation(targetId, amount, 'admin_add', true)
        if success then
            local message = string.format('Přidáno %d reputace hráči ID %d. Nová reputace: %d', amount, targetId, newReputation)
            if source == 0 then
                print('[28_gang] ' .. message)
            else
                NotifyPlayer(source, 'success', message)
            end
            TriggerClientEvent('28_gang:reputationUpdated', targetId, newReputation)
        end
    elseif action == 'remove' and targetId and amount then
        local success, newReputation = AddReputation(targetId, -amount, 'admin_remove', true)
        if success then
            local message = string.format('Odebráno %d reputace hráči ID %d. Nová reputace: %d', amount, targetId, newReputation)
            if source == 0 then
                print('[28_gang] ' .. message)
            else
                NotifyPlayer(source, 'success', message)
            end
            TriggerClientEvent('28_gang:reputationUpdated', targetId, newReputation)
        end
    elseif action == 'check' and targetId then
        local reputation = GetPlayerReputation(targetId)
        local message = string.format('Hráč ID %d má %d reputace', targetId, reputation)
        if source == 0 then
            print('[28_gang] ' .. message)
        else
            NotifyPlayer(source, 'info', message)
        end
    else
        local usage = 'Použití: /gang_reputation [add/remove/check] [player_id] [amount]'
        if source == 0 then
            print('[28_gang] ' .. usage)
        else
            NotifyPlayer(source, 'error', usage)
        end
    end
end, false)

-- Automatický úbytek reputace
if Config.Reputation.decayEnabled then
    CreateThread(function()
        while true do
            Wait(Config.Reputation.decayInterval)

            local Database = exports[GetCurrentResourceName()]:Database()
            local affected = Database.DecayReputation(1)
            
            if Config.Debug and affected > 0 then
                print(string.format('[28_gang] Reputation decay affected %d players', affected))
            end
        end
    end)
end

-- Export funkcí
exports('AddReputation', AddReputation)
exports('GetPlayerReputation', GetPlayerReputation)
exports('HasEnoughReputation', HasEnoughReputation)
