-- Test načítání scriptů
print('[28_gang] Test load script started')

CreateThread(function()
    Wait(5000)
    
    print('[28_gang] Testing Database export...')
    
    local success, Database = pcall(function()
        return exports[GetCurrentResourceName()]:Database()
    end)
    
    if success and Database then
        print('[28_gang] ✓ Database export loaded successfully')
        
        -- Test základních funkcí
        if Database.Init then
            print('[28_gang] ✓ Database.Init function found')
        end
        
        if Database.GetAllTerritories then
            print('[28_gang] ✓ Database.GetAllTerritories function found')
        end
        
        if Database.GetAllSprays then
            print('[28_gang] ✓ Database.GetAllSprays function found')
        end
        
    else
        print('[28_gang] ✗ Database export failed to load: ' .. tostring(Database))
    end
    
    print('[28_gang] Test completed')
end)
