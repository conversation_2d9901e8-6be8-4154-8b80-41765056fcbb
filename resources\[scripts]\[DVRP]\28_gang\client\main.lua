local PlayerData = {}
local isLoggedIn = false

-- Import modulů
local Zones = exports[GetCurrentResourceName()]:Zones()

-- Inicializace
CreateThread(function()
    while not exports.qbx_core do
        Wait(100)
    end

    PlayerData = exports.qbx_core.PlayerData
    isLoggedIn = true

    -- Načíst teritoria ze serveru
    TriggerServerEvent('28_gang:requestTerritories')
end)

-- Event handlers
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = exports.qbx_core.PlayerData
    isLoggedIn = true
    TriggerServerEvent('28_gang:requestTerritories')
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
    isLoggedIn = false
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

RegisterNetEvent('QBCore:Client:OnGangUpdate', function(GangInfo)
    PlayerData.gang = GangInfo
end)

-- Gang Editor Command
RegisterCommand(Config.Editor.command, function(source, args, rawCommand)
    if not isLoggedIn then return end
    
    -- Kontrola oprávnění
    if not exports.qbx_core:HasPermission(Config.Editor.permission) then
        TriggerEvent('28_gang:notification', 'error', Config.Texts[Config.Locale]['no_permission'])
        return
    end
    
    local action = args[1]
    
    if not action then
        -- Zobrazit nápovědu
        TriggerEvent('chat:addMessage', {
            color = {255, 255, 255},
            multiline = true,
            args = {"Gang Editor", "Použití:\n/" .. Config.Editor.command .. " create - Vytvořit nové teritorium\n/" .. Config.Editor.command .. " edit [id] - Upravit teritorium\n/" .. Config.Editor.command .. " delete [id] - Smazat teritorium\n/" .. Config.Editor.command .. " list - Seznam teritorií"}
        })
        return
    end
    
    if action == 'create' then
        Zones.StartEditor()
    elseif action == 'edit' then
        local territoryId = tonumber(args[2])
        if not territoryId then
            TriggerEvent('28_gang:notification', 'error', 'Zadej ID teritoria!')
            return
        end
        Zones.StartEditor(territoryId)
    elseif action == 'delete' then
        local territoryId = tonumber(args[2])
        if not territoryId then
            TriggerEvent('28_gang:notification', 'error', 'Zadej ID teritoria!')
            return
        end
        TriggerServerEvent('28_gang:deleteTerritory', territoryId)
    elseif action == 'list' then
        TriggerServerEvent('28_gang:listTerritories')
    else
        TriggerEvent('28_gang:notification', 'error', 'Neznámá akce!')
    end
end, false)

-- Notification system
RegisterNetEvent('28_gang:notification', function(type, message)
    if Config.Notifications.type == 'qbx' then
        if type == 'success' then
            exports.qbx_core:Notify(message, 'success')
        elseif type == 'error' then
            exports.qbx_core:Notify(message, 'error')
        elseif type == 'info' then
            exports.qbx_core:Notify(message, 'primary')
        else
            exports.qbx_core:Notify(message)
        end
    elseif Config.Notifications.type == 'ox' then
        lib.notify({
            title = 'Gang System',
            description = message,
            type = type,
            position = Config.Notifications.position
        })
    else
        -- Custom notification system
        print('[28_gang] ' .. type .. ': ' .. message)
    end
end)

-- Debug command
if Config.Debug then
    RegisterCommand('gangdebug', function(source, args, rawCommand)
        if not exports.qbx_core:HasPermission('admin') then return end
        
        local action = args[1]
        
        if action == 'territories' then
            local territories = Zones.GetAllTerritories()
            print('=== GANG TERRITORIES ===')
            for id, territory in pairs(territories) do
                print(string.format('ID: %s | Gang: %s | Coords: %s | Radius: %s', 
                    id, territory.gang, territory.coords, territory.radius))
            end
        elseif action == 'current' then
            local current = Zones.GetCurrentTerritory()
            if current then
                local territory = Zones.GetTerritoryById(current)
                print(string.format('Current Territory: %s (Gang: %s)', current, territory.gang))
            else
                print('Not in any territory')
            end
        elseif action == 'player' then
            print('=== PLAYER DATA ===')
            print('Gang: ' .. (PlayerData.gang and PlayerData.gang.name or 'None'))
            print('Job: ' .. (PlayerData.job and PlayerData.job.name or 'None'))
            print('Logged in: ' .. tostring(isLoggedIn))
        end
    end, false)
end

-- Export funkcí pro ostatní scripty
exports('GetCurrentTerritory', function()
    return Zones.GetCurrentTerritory()
end)

exports('GetTerritoryById', function(id)
    return Zones.GetTerritoryById(id)
end)

exports('GetPlayerGang', function()
    return PlayerData.gang
end)

exports('IsPlayerInGangTerritory', function(gang)
    local currentTerritory = Zones.GetCurrentTerritory()
    if not currentTerritory then return false end
    
    local territory = Zones.GetTerritoryById(currentTerritory)
    return territory and territory.gang == gang
end)
