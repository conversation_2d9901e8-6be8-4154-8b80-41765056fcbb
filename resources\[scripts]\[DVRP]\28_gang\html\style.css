* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

.hidden {
    display: none !important;
}

#app {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
}

.menu {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.95));
    border: 2px solid #333;
    border-radius: 15px;
    min-width: 400px;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
    pointer-events: all;
    backdrop-filter: blur(10px);
}

.menu-header {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
    padding: 15px 20px;
    border-radius: 13px 13px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-header h2 {
    font-size: 1.5em;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.close-btn {
    font-size: 2em;
    cursor: pointer;
    color: #ffffff;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #ff4444;
}

.menu-content {
    padding: 20px;
}

.gang-info {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.gang-info h3 {
    font-size: 1.3em;
    margin-bottom: 5px;
    color: #ff6b35;
}

.gang-info p {
    color: #cccccc;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.menu-buttons button,
.spray-actions button {
    padding: 12px 20px;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.menu-buttons button:hover,
.spray-actions button:hover {
    background: linear-gradient(90deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.territory-details {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.territory-details h3 {
    color: #ff6b35;
    margin-bottom: 10px;
}

.territory-details p {
    margin-bottom: 5px;
    color: #cccccc;
}

.spray-stats {
    margin-top: 15px;
}

.spray-stats h4 {
    color: #f7931e;
    margin-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.stat-item h3 {
    font-size: 2em;
    color: #ff6b35;
    margin-bottom: 5px;
}

.stat-item p {
    color: #cccccc;
    font-size: 0.9em;
}

.territory-list,
.spray-info {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
}

.territory-list h4,
.spray-info h3,
.spray-info h4 {
    color: #f7931e;
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
}

.spray-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.spray-actions button:nth-child(2) {
    background: linear-gradient(90deg, #2196F3, #1976D2);
}

.spray-actions button:nth-child(2):hover {
    background: linear-gradient(90deg, #1976D2, #2196F3);
}

#sprayBtn:disabled {
    background: linear-gradient(90deg, #666, #555);
    cursor: not-allowed;
    transform: none;
}

#sprayBtn:disabled:hover {
    background: linear-gradient(90deg, #666, #555);
    transform: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.notification {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.95));
    border: 2px solid #ff6b35;
    border-radius: 10px;
    padding: 15px 20px;
    min-width: 300px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    pointer-events: all;
    backdrop-filter: blur(5px);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content h3 {
    color: #ff6b35;
    margin-bottom: 5px;
    font-size: 1.1em;
}

.notification-content p {
    color: #cccccc;
    font-size: 0.9em;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #ff6b35;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #f7931e;
}

/* Reputation styles */
.reputation-overview {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.reputation-display h3 {
    font-size: 3em;
    color: #FFD700;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.reputation-bar {
    width: 100%;
    height: 25px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    margin: 15px 0;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.reputation-fill {
    height: 100%;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    width: 0%;
    transition: width 0.5s ease;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.leaderboard-section,
.top-gangs-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
}

.leaderboard-section h4,
.top-gangs-section h4 {
    color: #f7931e;
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 2px solid rgba(247, 147, 30, 0.3);
    padding-bottom: 10px;
}

.leaderboard-list,
.top-gangs-list {
    max-height: 200px;
    overflow-y: auto;
}

.leaderboard-item,
.top-gang-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border-left: 4px solid #ff6b35;
    transition: all 0.3s ease;
}

.leaderboard-item:hover,
.top-gang-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.leaderboard-item.current-player {
    border-left-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
}

.player-info,
.gang-info {
    display: flex;
    flex-direction: column;
}

.player-name,
.gang-name {
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 2px;
}

.player-stats,
.gang-stats {
    font-size: 0.8em;
    color: #cccccc;
}

.reputation-value {
    font-weight: bold;
    color: #FFD700;
    font-size: 1.1em;
}

.rank-badge {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
    .menu {
        min-width: 90vw;
        max-width: 90vw;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .spray-actions {
        flex-direction: column;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }

    .reputation-display h3 {
        font-size: 2em;
    }

    .leaderboard-item,
    .top-gang-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .reputation-value {
        margin-top: 5px;
    }
}
