local Zones = {}
local territories = {}
local currentTerritory = nil
local isInEditor = false
local editingTerritory = nil
local tempRadius = Config.Territories.defaultRadius

-- Import FreeCam
local FreeCam = exports[GetCurrentResourceName()]:FreeCam()

function Zones.LoadTerritories(data)
    territories = data or {}
    Zones.CreateBlips()
end

function Zones.CreateBlips()
    -- Smazat existující blipy
    for _, territory in pairs(territories) do
        if territory.blip then
            RemoveBlip(territory.blip)
        end
    end
    
    -- Vytvořit nové blipy
    if Config.Blips.showTerritories then
        for _, territory in pairs(territories) do
            if territory.coords and Config.Gangs[territory.gang] then
                local gangData = Config.Gangs[territory.gang]
                local blip = AddBlipForRadius(territory.coords.x, territory.coords.y, territory.coords.z, territory.radius)
                
                SetBlipColour(blip, gangData.blip.color)
                SetBlipAlpha(blip, 100)
                
                local centerBlip = AddBlipForCoord(territory.coords.x, territory.coords.y, territory.coords.z)
                SetBlipSprite(centerBlip, gangData.blip.sprite)
                SetBlipColour(centerBlip, gangData.blip.color)
                SetBlipScale(centerBlip, Config.Blips.territoryBlip.scale)
                SetBlipAsShortRange(centerBlip, Config.Blips.territoryBlip.shortRange)
                BeginTextCommandSetBlipName('STRING')
                AddTextComponentString(gangData.name .. ' Territory')
                EndTextCommandSetBlipName(centerBlip)
                
                territory.blip = blip
                territory.centerBlip = centerBlip
            end
        end
    end
end

function Zones.StartEditor(territoryId)
    if isInEditor then return end
    
    isInEditor = true
    editingTerritory = territoryId
    
    local coords
    if territoryId and territories[territoryId] then
        coords = territories[territoryId].coords
        tempRadius = territories[territoryId].radius
    else
        coords = GetEntityCoords(PlayerPedId())
        tempRadius = Config.Territories.defaultRadius
    end
    
    FreeCam.Start(coords)
    
    CreateThread(function()
        while isInEditor do
            Zones.EditorUpdate()
            Wait(0)
        end
    end)
end

function Zones.EditorUpdate()
    if not isInEditor then return end
    
    local coords = FreeCam.GetCoords()
    
    -- Upravit poloměr kolečkem myši
    if IsControlJustPressed(0, 241) then -- Mouse wheel up
        tempRadius = math.min(tempRadius + 5.0, Config.Territories.maxRadius)
    elseif IsControlJustPressed(0, 242) then -- Mouse wheel down
        tempRadius = math.max(tempRadius - 5.0, Config.Territories.minRadius)
    end
    
    -- Vykreslit náhled teritoria
    DrawMarker(1, coords.x, coords.y, coords.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
               tempRadius * 2.0, tempRadius * 2.0, 2.0, 255, 255, 255, 100, false, true, 2, false, nil, nil, false)
    
    -- Zobrazit nápovědu
    local helpText = Config.Texts[Config.Locale]['editor_help']
    if editingTerritory then
        helpText = string.format(Config.Texts[Config.Locale]['editor_edit'], editingTerritory) .. '\n' .. helpText
    else
        helpText = Config.Texts[Config.Locale]['editor_create'] .. '\n' .. helpText
    end
    
    SetTextFont(Config.UI.textFont)
    SetTextScale(Config.UI.textScale, Config.UI.textScale)
    SetTextColour(255, 255, 255, 255)
    SetTextOutline()
    SetTextEntry('STRING')
    AddTextComponentString(helpText .. '\nRadius: ' .. math.floor(tempRadius) .. 'm')
    DrawText(0.5, 0.1)
    
    -- Potvrdit
    if IsControlJustPressed(0, Config.Editor.keybind.confirm) then
        Zones.ConfirmEditor()
    end
    
    -- Zrušit
    if IsControlJustPressed(0, Config.Editor.keybind.cancel) then
        Zones.CancelEditor()
    end
end

function Zones.ConfirmEditor()
    if not isInEditor then return end
    
    local coords = FreeCam.GetCoords()
    
    if editingTerritory then
        -- Upravit existující teritorium
        TriggerServerEvent('28_gang:updateTerritory', editingTerritory, coords, tempRadius)
    else
        -- Vytvořit nové teritorium
        TriggerServerEvent('28_gang:createTerritory', coords, tempRadius)
    end
    
    Zones.CancelEditor()
end

function Zones.CancelEditor()
    if not isInEditor then return end
    
    isInEditor = false
    editingTerritory = nil
    FreeCam.Stop()
end

function Zones.CheckPlayerInTerritory()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local newTerritory = nil
    
    for id, territory in pairs(territories) do
        if territory.coords then
            local distance = #(playerCoords - territory.coords)
            if distance <= territory.radius then
                newTerritory = id
                break
            end
        end
    end
    
    if newTerritory ~= currentTerritory then
        if currentTerritory and territories[currentTerritory] then
            local oldGang = territories[currentTerritory].gang
            if Config.Gangs[oldGang] then
                TriggerEvent('28_gang:notification', 'info', string.format(Config.Texts[Config.Locale]['territory_exit'], Config.Gangs[oldGang].name))
            end
        end
        
        if newTerritory and territories[newTerritory] then
            local newGang = territories[newTerritory].gang
            if Config.Gangs[newGang] then
                TriggerEvent('28_gang:notification', 'info', string.format(Config.Texts[Config.Locale]['territory_enter'], Config.Gangs[newGang].name))
            end
        end
        
        currentTerritory = newTerritory
        TriggerServerEvent('28_gang:playerTerritoryChanged', currentTerritory)
    end
end

function Zones.GetCurrentTerritory()
    return currentTerritory
end

function Zones.GetTerritoryById(id)
    return territories[id]
end

function Zones.GetAllTerritories()
    return territories
end

-- Event handlers
RegisterNetEvent('28_gang:territoriesLoaded', function(data)
    Zones.LoadTerritories(data)
end)

RegisterNetEvent('28_gang:territoryCreated', function(id, data)
    territories[id] = data
    Zones.CreateBlips()
    TriggerEvent('28_gang:notification', 'success', Config.Texts[Config.Locale]['territory_created'])
end)

RegisterNetEvent('28_gang:territoryUpdated', function(id, data)
    territories[id] = data
    Zones.CreateBlips()
    TriggerEvent('28_gang:notification', 'success', Config.Texts[Config.Locale]['territory_updated'])
end)

RegisterNetEvent('28_gang:territoryDeleted', function(id)
    if territories[id] then
        if territories[id].blip then
            RemoveBlip(territories[id].blip)
        end
        if territories[id].centerBlip then
            RemoveBlip(territories[id].centerBlip)
        end
        territories[id] = nil
    end
    TriggerEvent('28_gang:notification', 'success', Config.Texts[Config.Locale]['territory_deleted'])
end)

-- Main thread pro kontrolu teritorií
CreateThread(function()
    while true do
        if not isInEditor then
            Zones.CheckPlayerInTerritory()
        end
        Wait(1000)
    end
end)

-- Export pro ostatní moduly
exports('Zones', Zones)
return Zones
